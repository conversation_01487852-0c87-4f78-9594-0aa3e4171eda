/* 头像增强特效库 - 基于优化呼吸光环的多特效组合 */

/* ===== CSS变量定义 ===== */
:root {
    /* 呼吸光环变量 */
    --ab-duration: 4s;
    --ab-red: #f00;
    --ab-green: #0f0;
    --ab-blue: #00f;
    --ab-blur-s: 4px;
    --ab-blur-l: 16px;
    --ab-scale: 1.15;
    --ab-timing: cubic-bezier(.18,.89,.32,1.28);
    
    /* 新增特效变量 */
    --neon-colors: #ff0000, #ff7300, #fffb00, #48ff00, #00ffd5, #002bff, #7a00ff, #ff00c8, #ff0000;
    --pulse-color: #007bff;
    --ripple-color: rgba(0, 123, 255, 0.6);
    --sparkle-color: #ffd700;
    --flow-speed: 2s;
    --rotate-speed: 3s;
}

/* ===== 基础头像样式（保持原有呼吸光环） ===== */
.avatar {
    border-radius: 50%;
    transform: translateZ(0);
    will-change: transform, box-shadow;
    animation: breathe var(--ab-duration) ease-in-out infinite;
    transition: transform .35s var(--ab-timing);
    position: relative;
    display: inline-block;
}

@keyframes breathe {
    0%, 100% { box-shadow: 0 0 var(--ab-blur-s) var(--ab-red); }
    25%, 75% { box-shadow: 0 0 var(--ab-blur-l) var(--ab-green); }
    50% { box-shadow: 0 0 var(--ab-blur-s) var(--ab-blue); }
}

.avatar:hover, .avatar:focus {
    transform: translateZ(0) scale(var(--ab-scale));
}

.avatar:focus {
    outline: 2px solid #007acc;
    outline-offset: 2px;
}

/* ===== 速度修饰符 ===== */
.avatar--fast { --ab-duration: 2s; }
.avatar--slow { --ab-duration: 8s; }
.avatar--subtle { --ab-blur-l: 8px; }

/* ===== 新增特效 ===== */

/* 1. 霓虹边框特效 */
.avatar--neon {
    padding: 4px;
    background: linear-gradient(45deg, var(--neon-colors));
    background-size: 400% 400%;
    animation: breathe var(--ab-duration) ease-in-out infinite, neon-gradient 3s ease infinite;
}

.avatar--neon img {
    border-radius: 50%;
    display: block;
    width: 100%;
    height: 100%;
}

@keyframes neon-gradient {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* 2. 3D翻转特效 */
.avatar--flip {
    perspective: 1000px;
    transform-style: preserve-3d;
}

.avatar--flip:hover {
    animation-play-state: paused;
    transform: translateZ(0) rotateY(180deg);
    transition: transform 0.6s ease;
}

.avatar--flip .back {
    position: absolute;
    top: 0; left: 0;
    backface-visibility: hidden;
    transform: rotateY(180deg);
    border-radius: 50%;
}

/* 3. 水波纹点击特效 */
.avatar--ripple::before {
    content: '';
    position: absolute;
    top: 50%; left: 50%;
    width: 0; height: 0;
    border: 2px solid var(--ripple-color);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    opacity: 0;
    pointer-events: none;
}

.avatar--ripple:active::before {
    animation: ripple-expand 0.6s ease-out;
}

@keyframes ripple-expand {
    0% {
        width: 0; height: 0;
        opacity: 1;
    }
    100% {
        width: 200%; height: 200%;
        opacity: 0;
    }
}

/* 4. 旋转动画特效 */
.avatar--rotate {
    animation: breathe var(--ab-duration) ease-in-out infinite, rotate-360 var(--rotate-speed) linear infinite;
}

@keyframes rotate-360 {
    0% { transform: translateZ(0) rotate(0deg); }
    100% { transform: translateZ(0) rotate(360deg); }
}

/* 5. 发光脉冲特效 */
.avatar--pulse {
    animation: breathe var(--ab-duration) ease-in-out infinite, pulse-glow 2s ease-in-out infinite;
}

@keyframes pulse-glow {
    0%, 100% {
        filter: brightness(1);
        box-shadow: 0 0 var(--ab-blur-s) var(--ab-red), 0 0 10px var(--pulse-color);
    }
    50% {
        filter: brightness(1.2);
        box-shadow: 0 0 var(--ab-blur-l) var(--ab-green), 0 0 20px var(--pulse-color);
    }
}

/* 6. 边框流光特效 */
.avatar--flow::after {
    content: '';
    position: absolute;
    top: -3px; left: -3px; right: -3px; bottom: -3px;
    background: conic-gradient(transparent, transparent, var(--pulse-color), transparent, transparent);
    border-radius: 50%;
    animation: flow-rotate var(--flow-speed) linear infinite;
    z-index: -1;
}

@keyframes flow-rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 7. 星光闪烁特效 */
.avatar--sparkle::before {
    content: '✨';
    position: absolute;
    top: 10%; right: 10%;
    font-size: 16px;
    color: var(--sparkle-color);
    animation: sparkle-twinkle 2s ease-in-out infinite;
    pointer-events: none;
}

@keyframes sparkle-twinkle {
    0%, 100% { opacity: 0; transform: scale(0) rotate(0deg); }
    50% { opacity: 1; transform: scale(1) rotate(180deg); }
}

/* 8. 3D立体浮起特效 */
.avatar--float {
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
}

.avatar--float:hover {
    transform: translateZ(0) translateY(-10px) rotateX(10deg) scale(var(--ab-scale));
    box-shadow: 0 15px 30px rgba(0,0,0,0.4);
}

/* ===== 特效组合类 ===== */

/* 游戏风格组合 */
.avatar--gaming {
    animation: breathe var(--ab-duration) ease-in-out infinite, neon-gradient 3s ease infinite;
    padding: 4px;
    background: linear-gradient(45deg, var(--neon-colors));
    background-size: 400% 400%;
}

.avatar--gaming::after {
    content: '';
    position: absolute;
    top: -3px; left: -3px; right: -3px; bottom: -3px;
    background: conic-gradient(transparent, transparent, var(--pulse-color), transparent, transparent);
    border-radius: 50%;
    animation: flow-rotate var(--flow-speed) linear infinite;
    z-index: -1;
}

/* 科技风格组合 */
.avatar--tech {
    animation: breathe var(--ab-duration) ease-in-out infinite, pulse-glow 2s ease-in-out infinite;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.avatar--tech:hover {
    transform: translateZ(0) translateY(-5px) scale(var(--ab-scale));
    box-shadow: 0 15px 30px rgba(0,123,255,0.4);
}

/* 魔法风格组合 */
.avatar--magic {
    animation: breathe var(--ab-duration) ease-in-out infinite;
}

.avatar--magic::before {
    content: '✨';
    position: absolute;
    top: 10%; right: 10%;
    font-size: 16px;
    color: var(--sparkle-color);
    animation: sparkle-twinkle 2s ease-in-out infinite;
    pointer-events: none;
}

.avatar--magic::after {
    content: '⭐';
    position: absolute;
    bottom: 10%; left: 10%;
    font-size: 12px;
    color: var(--sparkle-color);
    animation: sparkle-twinkle 2s ease-in-out infinite;
    animation-delay: 1s;
    pointer-events: none;
}

/* ===== 主题适配 ===== */
[data-theme="dark"] {
    --ab-red: #ff6b6b;
    --ab-green: #4ecdc4;
    --ab-blue: #45b7d1;
    --pulse-color: #64b5f6;
    --sparkle-color: #ffeb3b;
}

[data-theme="minimal"] {
    --ab-red: #666;
    --ab-green: #999;
    --ab-blue: #333;
    --ab-blur-l: 6px;
    --pulse-color: #888;
}

/* ===== 响应式适配 ===== */
@media (max-width: 768px) {
    .avatar {
        --ab-duration: 6s;
        --ab-scale: 1.1;
        --flow-speed: 3s;
        --rotate-speed: 4s;
    }
}

@media (max-width: 480px) {
    .avatar {
        --ab-blur-l: 12px;
    }
    
    .avatar--sparkle::before {
        font-size: 12px;
    }
}

/* ===== 无障碍支持 ===== */
@media (prefers-reduced-motion: reduce) {
    .avatar {
        animation: none;
        box-shadow: 0 0 var(--ab-blur-s) rgba(0,0,0,.2);
    }
    
    .avatar:hover {
        transform: translateZ(0) scale(1.05);
        transition-duration: .2s;
    }
    
    .avatar--neon,
    .avatar--rotate,
    .avatar--pulse,
    .avatar--flow,
    .avatar--sparkle,
    .avatar--gaming,
    .avatar--tech,
    .avatar--magic {
        animation: none;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .avatar {
        --ab-red: #000;
        --ab-green: #fff;
        --ab-blue: #000;
        filter: contrast(2);
        border: 2px solid currentColor;
    }
}

/* 头像呼吸光环优化版 - 保持核心功能，提升性能 */

:root {
    --ab-duration: 4s;
    --ab-red: #f00;
    --ab-green: #0f0;
    --ab-blue: #00f;
    --ab-blur-s: 4px;
    --ab-blur-l: 16px;
    --ab-scale: 1.15;
    --ab-timing: cubic-bezier(.18,.89,.32,1.28);
}

.avatar {
    border-radius: 50%;
    transform: translateZ(0);
    will-change: transform, box-shadow;
    animation: breathe var(--ab-duration) ease-in-out infinite;
    transition: transform .35s var(--ab-timing);
}

@keyframes breathe {
    0%, 100% { box-shadow: 0 0 var(--ab-blur-s) var(--ab-red); }
    25%, 75% { box-shadow: 0 0 var(--ab-blur-l) var(--ab-green); }
    50% { box-shadow: 0 0 var(--ab-blur-s) var(--ab-blue); }
}

.avatar:hover, .avatar:focus {
    transform: translateZ(0) scale(var(--ab-scale));
}

.avatar:focus {
    outline: 2px solid #007acc;
    outline-offset: 2px;
}

/* 修饰符 */
.avatar--fast { --ab-duration: 2s; }
.avatar--slow { --ab-duration: 8s; }
.avatar--subtle { --ab-blur-l: 8px; }

/* 主题 */
[data-theme="dark"] {
    --ab-red: #ff6b6b;
    --ab-green: #4ecdc4;
    --ab-blue: #45b7d1;
}

/* 响应式 */
@media (max-width: 768px) {
    .avatar {
        --ab-duration: 6s;
        --ab-scale: 1.1;
    }
}

/* 无障碍 */
@media (prefers-reduced-motion: reduce) {
    .avatar {
        animation: none;
        box-shadow: 0 0 var(--ab-blur-s) rgba(0,0,0,.2);
    }
    .avatar:hover {
        transform: translateZ(0) scale(1.05);
        transition-duration: .2s;
    }
}

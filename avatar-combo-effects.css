/*头像呼吸光环+5种组合特效类（基于优化代码扩展）*/

/* ===== 基础呼吸光环代码（保持不变） ===== */
:root {
    --ab-duration: 4s;
    --ab-red: #f00;
    --ab-green: #0f0;
    --ab-blue: #00f;
    --ab-blur-s: 4px;
    --ab-blur-l: 16px;
    --ab-scale: 1.15;
    --ab-timing: cubic-bezier(.18,.89,.32,1.28);
}

.avatar {
    border-radius: 50%;
    transform: translateZ(0);
    will-change: transform, box-shadow;
    animation: breathe var(--ab-duration) ease-in-out infinite;
    transition: transform .35s var(--ab-timing);
}

@keyframes breathe {
    0%, 100% { box-shadow: 0 0 var(--ab-blur-s) var(--ab-red); }
    25%, 75% { box-shadow: 0 0 var(--ab-blur-l) var(--ab-green); }
    50% { box-shadow: 0 0 var(--ab-blur-s) var(--ab-blue); }
}

.avatar:hover, .avatar:focus {
    transform: translateZ(0) scale(var(--ab-scale));
}

.avatar:focus {
    outline: 2px solid #007acc;
    outline-offset: 2px;
}

.avatar--fast { --ab-duration: 2s; }
.avatar--slow { --ab-duration: 8s; }
.avatar--subtle { --ab-blur-l: 8px; }

[data-theme="dark"] {
    --ab-red: #ff6b6b;
    --ab-green: #4ecdc4;
    --ab-blue: #45b7d1;
}

@media (max-width: 768px) {
    .avatar {
        --ab-duration: 6s;
        --ab-scale: 1.1;
    }
}

@media (prefers-reduced-motion: reduce) {
    .avatar {
        animation: none;
        box-shadow: 0 0 var(--ab-blur-s) rgba(0,0,0,.2);
    }
    .avatar:hover {
        transform: translateZ(0) scale(1.05);
        transition-duration: .2s;
    }
}

/* ===== 组合特效类扩展 ===== */

/* 1. 全能组合 - 基于呼吸光环增强 */
.avatar--ultimate {
    box-shadow: 0 5px 15px rgba(0,0,0,0.2);
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.avatar--ultimate:hover, .avatar--ultimate:focus {
    transform: translateZ(0) scale(1.2) translateY(-10px) rotate(5deg);
    box-shadow: 
        0 20px 40px rgba(0,0,0,0.3),
        0 0 30px rgba(0, 123, 255, 0.6),
        0 0 0 5px #007bff,
        0 0 var(--ab-blur-l) var(--ab-green);
    filter: brightness(1.1) saturate(1.2);
    animation-play-state: running;
}

/* 2. 科技感组合 - 基于呼吸光环+科技元素 */
.avatar--tech {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.avatar--tech::before {
    content: '';
    position: absolute;
    top: 0; left: -100%;
    width: 100%; height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0,255,255,0.8), transparent);
    transition: left 0.6s ease;
    border-radius: 50%;
    z-index: 1;
    pointer-events: none;
}

.avatar--tech::after {
    content: '';
    position: absolute;
    top: -3px; left: -3px; right: -3px; bottom: -3px;
    background: conic-gradient(transparent, #00ffff, transparent, #ff00ff, transparent);
    border-radius: 50%;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: -1;
}

.avatar--tech:hover, .avatar--tech:focus {
    transform: translateZ(0) perspective(1000px) rotateX(10deg) rotateY(10deg) scale(1.1);
    box-shadow: 
        0 0 30px rgba(0,255,255,0.6),
        0 0 var(--ab-blur-l) var(--ab-blue);
    filter: brightness(1.2) contrast(1.1);
}

.avatar--tech:hover::before, .avatar--tech:focus::before {
    left: 100%;
}

.avatar--tech:hover::after, .avatar--tech:focus::after {
    opacity: 1;
    animation: tech-rotate 2s linear infinite;
}

@keyframes tech-rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 3. 魔法风格组合 - 基于呼吸光环+魔法元素 */
.avatar--magic {
    position: relative;
    transition: all 0.3s ease;
}

.avatar--magic::before {
    content: '✨';
    position: absolute;
    top: -10px; right: -10px;
    font-size: 20px;
    opacity: 0;
    transition: all 0.3s ease;
    animation: sparkle-float 2s ease-in-out infinite;
    pointer-events: none;
    z-index: 2;
}

.avatar--magic::after {
    content: '⭐';
    position: absolute;
    bottom: -10px; left: -10px;
    font-size: 16px;
    opacity: 0;
    transition: all 0.3s ease;
    animation: sparkle-float 2s ease-in-out infinite;
    animation-delay: 1s;
    pointer-events: none;
    z-index: 2;
}

.avatar--magic:hover, .avatar--magic:focus {
    transform: translateZ(0) scale(1.15);
    box-shadow: 
        0 0 20px #ff69b4,
        0 0 40px #9370db,
        0 0 60px #00ced1,
        0 0 var(--ab-blur-l) var(--ab-green);
    filter: brightness(1.2) saturate(1.3);
    animation: breathe var(--ab-duration) ease-in-out infinite, magic-wiggle 0.6s ease-in-out;
}

.avatar--magic:hover::before,
.avatar--magic:hover::after,
.avatar--magic:focus::before,
.avatar--magic:focus::after {
    opacity: 1;
}

@keyframes sparkle-float {
    0%, 100% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-5px) rotate(180deg); }
}

@keyframes magic-wiggle {
    0%, 100% { transform: translateZ(0) scale(1.15) rotate(0deg); }
    25% { transform: translateZ(0) scale(1.15) rotate(2deg); }
    75% { transform: translateZ(0) scale(1.15) rotate(-2deg); }
}

/* 4. 游戏风格组合 - 基于呼吸光环+游戏元素 */
.avatar--gaming {
    position: relative;
    transition: all 0.3s ease;
    padding: 3px;
    background: linear-gradient(45deg, #ff0080, #00ff80, #8000ff, #ff0080);
    background-size: 400% 400%;
    border-radius: 50%;
}

.avatar--gaming img {
    border-radius: 50%;
    display: block;
    width: 100%;
    height: 100%;
}

.avatar--gaming:hover, .avatar--gaming:focus {
    transform: translateZ(0) scale(1.2);
    animation: 
        breathe var(--ab-duration) ease-in-out infinite,
        gaming-pulse 1s ease-in-out infinite,
        gaming-border 2s ease infinite,
        gaming-bounce 0.6s ease;
    box-shadow: 
        0 0 30px #ff0080,
        0 0 50px #00ff80,
        0 0 70px #8000ff,
        0 0 var(--ab-blur-l) var(--ab-red);
}

@keyframes gaming-pulse {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.3); }
}

@keyframes gaming-border {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

@keyframes gaming-bounce {
    0%, 100% { transform: translateZ(0) scale(1.2) translateY(0); }
    25% { transform: translateZ(0) scale(1.2) translateY(-8px); }
    50% { transform: translateZ(0) scale(1.2) translateY(0); }
    75% { transform: translateZ(0) scale(1.2) translateY(-4px); }
}

/* 5. 简约现代组合 - 基于呼吸光环简化版 */
.avatar--modern {
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.avatar--modern:hover, .avatar--modern:focus {
    transform: translateZ(0) scale(1.08) translateY(-2px);
    box-shadow: 
        0 8px 25px rgba(0,0,0,0.15),
        0 0 0 1px rgba(0,123,255,0.3),
        0 0 var(--ab-blur-s) var(--ab-blue);
    filter: brightness(1.05);
}

/* ===== 组合特效的响应式和无障碍适配 ===== */

/* 移动端优化 */
@media (max-width: 768px) {
    .avatar--ultimate,
    .avatar--tech,
    .avatar--magic,
    .avatar--gaming,
    .avatar--modern {
        --ab-scale: 1.1;
    }
    
    .avatar--ultimate:hover, .avatar--ultimate:focus {
        transform: translateZ(0) scale(1.15) translateY(-5px) rotate(2deg);
    }
    
    .avatar--tech:hover, .avatar--tech:focus {
        transform: translateZ(0) scale(1.1);
    }
    
    .avatar--gaming:hover, .avatar--gaming:focus {
        transform: translateZ(0) scale(1.15);
    }
}

/* 减少动态效果支持 */
@media (prefers-reduced-motion: reduce) {
    .avatar--ultimate,
    .avatar--tech,
    .avatar--magic,
    .avatar--gaming,
    .avatar--modern {
        animation: none;
    }
    
    .avatar--ultimate:hover,
    .avatar--tech:hover,
    .avatar--magic:hover,
    .avatar--gaming:hover,
    .avatar--modern:hover,
    .avatar--ultimate:focus,
    .avatar--tech:focus,
    .avatar--magic:focus,
    .avatar--gaming:focus,
    .avatar--modern:focus {
        animation: none;
        transform: translateZ(0) scale(1.05);
        transition-duration: .2s;
    }
    
    .avatar--tech::before,
    .avatar--tech::after,
    .avatar--magic::before,
    .avatar--magic::after {
        display: none;
    }
}

/* 暗色主题适配 */
[data-theme="dark"] .avatar--ultimate:hover,
[data-theme="dark"] .avatar--ultimate:focus {
    box-shadow: 
        0 20px 40px rgba(0,0,0,0.5),
        0 0 30px rgba(100, 181, 246, 0.6),
        0 0 0 5px #64b5f6,
        0 0 var(--ab-blur-l) var(--ab-green);
}

[data-theme="dark"] .avatar--modern:hover,
[data-theme="dark"] .avatar--modern:focus {
    box-shadow: 
        0 8px 25px rgba(0,0,0,0.3),
        0 0 0 1px rgba(100,181,246,0.4),
        0 0 var(--ab-blur-s) var(--ab-blue);
}

/* 头像特效完整代码库 - 所有64种特效实现 */

/* ===== 交互触发类特效 ===== */

/* 1. 悬停触发 */
.avatar-hover-trigger {
    border-radius: 50%;
    transition: all 0.3s ease;
}

.avatar-hover-trigger:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 10px 20px rgba(0,0,0,0.3);
}

/* 2. 点击触发 */
.avatar-click-trigger {
    border-radius: 50%;
    transition: all 0.2s ease;
}

.avatar-click-trigger:active {
    transform: scale(0.95);
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

/* 3. 长按触发 */
.avatar-long-press {
    border-radius: 50%;
    position: relative;
}

.avatar-long-press::before {
    content: '';
    position: absolute;
    top: -5px; left: -5px; right: -5px; bottom: -5px;
    border-radius: 50%;
    border: 3px solid transparent;
    border-top-color: #007bff;
    animation: long-press-progress 2s linear;
    animation-play-state: paused;
}

.avatar-long-press:active::before {
    animation-play-state: running;
}

@keyframes long-press-progress {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 4. 滚动触发 */
.avatar-scroll-trigger {
    border-radius: 50%;
    transition: all 0.5s ease;
    opacity: 0;
    transform: translateY(50px);
}

.avatar-scroll-trigger.in-view {
    opacity: 1;
    transform: translateY(0);
}

/* 5. 焦点触发 */
.avatar-focus-trigger {
    border-radius: 50%;
    transition: all 0.3s ease;
}

.avatar-focus-trigger:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.5);
    transform: scale(1.05);
}

/* 6. 鼠标轨迹 */
.avatar-mouse-trail {
    border-radius: 50%;
    position: relative;
}

.avatar-mouse-trail::before {
    content: '';
    position: absolute;
    width: 4px; height: 4px;
    background: #007bff;
    border-radius: 50%;
    pointer-events: none;
    animation: trail-fade 1s ease-out forwards;
}

@keyframes trail-fade {
    0% { opacity: 1; transform: scale(1); }
    100% { opacity: 0; transform: scale(0); }
}

/* 7. 双击触发 */
.avatar-double-click {
    border-radius: 50%;
    position: relative;
}

.avatar-double-click.double-clicked::before {
    content: '❤️';
    position: absolute;
    top: 50%; left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    animation: heart-pop 0.6s ease-out;
}

@keyframes heart-pop {
    0% { transform: translate(-50%, -50%) scale(0); }
    50% { transform: translate(-50%, -50%) scale(1.2); }
    100% { transform: translate(-50%, -50%) scale(1); }
}

/* 8. 键盘快捷键 */
.avatar-keyboard {
    border-radius: 50%;
    transition: all 0.3s ease;
}

.avatar-keyboard.key-pressed {
    transform: scale(1.2) rotate(360deg);
    box-shadow: 0 0 20px #007bff;
}

/* ===== 创意组合特效 ===== */

/* 1. 水波纹扩散 */
.avatar-water-ripple {
    border-radius: 50%;
    position: relative;
    overflow: hidden;
}

.avatar-water-ripple::before {
    content: '';
    position: absolute;
    top: 50%; left: 50%;
    width: 0; height: 0;
    background: radial-gradient(circle, rgba(255,255,255,0.6) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: water-ripple 2s ease-out infinite;
}

@keyframes water-ripple {
    0% {
        width: 0; height: 0;
        opacity: 1;
    }
    100% {
        width: 300%; height: 300%;
        opacity: 0;
    }
}

/* 2. 磁场效应 */
.avatar-magnetic {
    border-radius: 50%;
    position: relative;
}

.avatar-magnetic::before,
.avatar-magnetic::after {
    content: '';
    position: absolute;
    top: 50%; left: 50%;
    width: 150%; height: 150%;
    border: 2px solid rgba(0, 123, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: magnetic-field 3s ease-in-out infinite;
}

.avatar-magnetic::after {
    animation-delay: 1.5s;
}

@keyframes magnetic-field {
    0%, 100% {
        transform: translate(-50%, -50%) scale(0.8);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2);
        opacity: 1;
    }
}

/* 3. 能量场 */
.avatar-energy-field {
    border-radius: 50%;
    position: relative;
    animation: energy-pulse 2s ease-in-out infinite;
}

.avatar-energy-field::before {
    content: '';
    position: absolute;
    top: -20%; left: -20%; right: -20%; bottom: -20%;
    background: radial-gradient(circle, transparent 60%, rgba(0, 255, 255, 0.3) 70%, transparent 80%);
    border-radius: 50%;
    animation: energy-rotate 4s linear infinite;
}

@keyframes energy-pulse {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.3) contrast(1.2); }
}

@keyframes energy-rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 4. 时空扭曲 */
.avatar-space-warp {
    border-radius: 50%;
    position: relative;
    overflow: hidden;
}

.avatar-space-warp::before {
    content: '';
    position: absolute;
    top: -50%; left: -50%; right: -50%; bottom: -50%;
    background: conic-gradient(transparent, rgba(255,255,255,0.1), transparent);
    animation: space-warp 3s ease-in-out infinite;
}

@keyframes space-warp {
    0%, 100% {
        transform: rotate(0deg) scale(1);
        filter: blur(0px);
    }
    50% {
        transform: rotate(180deg) scale(1.2);
        filter: blur(2px);
    }
}

/* 5. 像素化效果 */
.avatar-pixelate {
    border-radius: 50%;
    transition: all 0.5s steps(8, end);
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
}

.avatar-pixelate:hover {
    filter: contrast(1.2) saturate(1.5);
    transform: scale(1.1);
}

/* 6. 马赛克转换 */
.avatar-mosaic {
    border-radius: 50%;
    position: relative;
    overflow: hidden;
}

.avatar-mosaic::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: 
        repeating-linear-gradient(0deg, transparent, transparent 2px, rgba(0,0,0,0.1) 2px, rgba(0,0,0,0.1) 4px),
        repeating-linear-gradient(90deg, transparent, transparent 2px, rgba(0,0,0,0.1) 2px, rgba(0,0,0,0.1) 4px);
    opacity: 0;
    transition: opacity 0.5s ease;
}

.avatar-mosaic:hover::before {
    opacity: 1;
}

/* 7. 粒子爆炸 */
.avatar-particle-explosion {
    border-radius: 50%;
    position: relative;
}

.avatar-particle-explosion.exploding::before {
    content: '💥';
    position: absolute;
    top: 50%; left: 50%;
    transform: translate(-50%, -50%);
    font-size: 0;
    animation: particle-explode 1s ease-out;
}

@keyframes particle-explode {
    0% {
        font-size: 0;
        opacity: 0;
    }
    50% {
        font-size: 48px;
        opacity: 1;
    }
    100% {
        font-size: 64px;
        opacity: 0;
        transform: translate(-50%, -50%) scale(2);
    }
}

/* 8. 碎片重组 */
.avatar-fragment-rebuild {
    border-radius: 50%;
    position: relative;
    animation: fragment-rebuild 3s ease-in-out infinite;
}

@keyframes fragment-rebuild {
    0%, 100% {
        transform: scale(1);
        filter: blur(0px);
    }
    25% {
        transform: scale(0.8) rotate(5deg);
        filter: blur(1px);
    }
    50% {
        transform: scale(0.6) rotate(-5deg);
        filter: blur(2px);
    }
    75% {
        transform: scale(0.9) rotate(2deg);
        filter: blur(0.5px);
    }
}

/* ===== 响应式和无障碍支持 ===== */

/* 移动端优化 */
@media (max-width: 768px) {
    .avatar-mobile-optimized {
        animation-duration: 1.5s !important;
        transform: scale(0.9) !important;
    }
}

/* 减少动态效果支持 */
@media (prefers-reduced-motion: reduce) {
    .avatar-accessible {
        animation: none !important;
        transition: transform 0.2s ease !important;
    }
    
    .avatar-accessible:hover {
        transform: scale(1.05) !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .avatar-high-contrast {
        filter: contrast(2) !important;
        border: 2px solid currentColor !important;
    }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
    .avatar-dark-theme {
        filter: brightness(0.9) contrast(1.1);
        box-shadow: 0 0 10px rgba(255,255,255,0.1);
    }
}

/* 剩余头像特效代码实现 */

/* ===== 边框装饰类特效 (续) ===== */

/* 3. 边框渐变 */
.avatar-gradient-border {
    border-radius: 50%;
    position: relative;
    padding: 3px;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #ffeaa7);
    background-size: 300% 300%;
    animation: gradient-shift 3s ease infinite;
}

.avatar-gradient-border img {
    border-radius: 50%;
    display: block;
}

@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

/* 4. 多层边框 */
.avatar-multi-border {
    border-radius: 50%;
    position: relative;
    border: 2px solid #007bff;
    box-shadow: 
        0 0 0 4px #ffffff,
        0 0 0 6px #28a745,
        0 0 0 8px #ffffff,
        0 0 0 10px #dc3545;
}

/* 5. 边框闪烁 */
.avatar-blink-border {
    border-radius: 50%;
    border: 3px solid #ffc107;
    animation: border-blink 1s ease-in-out infinite;
}

@keyframes border-blink {
    0%, 100% { border-color: #ffc107; opacity: 1; }
    50% { border-color: transparent; opacity: 0.5; }
}

/* 6. 边框流光 */
.avatar-flowing-border {
    border-radius: 50%;
    position: relative;
    overflow: hidden;
}

.avatar-flowing-border::before {
    content: '';
    position: absolute;
    top: -2px; left: -2px; right: -2px; bottom: -2px;
    background: conic-gradient(transparent, transparent, #007bff, transparent, transparent);
    border-radius: 50%;
    animation: flow-rotate 2s linear infinite;
}

@keyframes flow-rotate {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 7. 几何图形边框 */
.avatar-hexagon {
    width: 100px;
    height: 100px;
    position: relative;
    margin: 20px;
}

.avatar-hexagon::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: #007bff;
    clip-path: polygon(50% 0%, 100% 25%, 100% 75%, 50% 100%, 0% 75%, 0% 25%);
    animation: hex-pulse 2s ease-in-out infinite;
}

@keyframes hex-pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* 8. 边框粒子 */
.avatar-particle-border {
    border-radius: 50%;
    position: relative;
}

.avatar-particle-border::before,
.avatar-particle-border::after {
    content: '●';
    position: absolute;
    color: #007bff;
    font-size: 8px;
    animation: particle-orbit 3s linear infinite;
}

.avatar-particle-border::before {
    top: -5px; left: 50%;
    transform: translateX(-50%);
}

.avatar-particle-border::after {
    bottom: -5px; left: 50%;
    transform: translateX(-50%);
    animation-delay: 1.5s;
}

@keyframes particle-orbit {
    0% { transform: translateX(-50%) rotate(0deg) translateX(60px) rotate(0deg); }
    100% { transform: translateX(-50%) rotate(360deg) translateX(60px) rotate(-360deg); }
}

/* ===== 滤镜效果类特效 ===== */

/* 1. 模糊到清晰 */
.avatar-blur-focus {
    border-radius: 50%;
    filter: blur(3px);
    transition: filter 0.5s ease;
}

.avatar-blur-focus:hover {
    filter: blur(0px);
}

/* 2. 黑白到彩色 */
.avatar-grayscale {
    border-radius: 50%;
    filter: grayscale(100%);
    transition: filter 0.5s ease;
}

.avatar-grayscale:hover {
    filter: grayscale(0%);
}

/* 3. 色相旋转 */
.avatar-hue-rotate {
    border-radius: 50%;
    animation: hue-rotation 5s linear infinite;
}

@keyframes hue-rotation {
    0% { filter: hue-rotate(0deg); }
    100% { filter: hue-rotate(360deg); }
}

/* 4. 饱和度变化 */
.avatar-saturation {
    border-radius: 50%;
    animation: saturation-pulse 3s ease-in-out infinite;
}

@keyframes saturation-pulse {
    0%, 100% { filter: saturate(1); }
    50% { filter: saturate(2); }
}

/* 5. 亮度调节 */
.avatar-brightness {
    border-radius: 50%;
    animation: brightness-pulse 2s ease-in-out infinite;
}

@keyframes brightness-pulse {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.3); }
}

/* 6. 对比度增强 */
.avatar-contrast {
    border-radius: 50%;
    animation: contrast-pulse 3s ease-in-out infinite;
}

@keyframes contrast-pulse {
    0%, 100% { filter: contrast(1); }
    50% { filter: contrast(1.5); }
}

/* 7. 复古滤镜 */
.avatar-vintage {
    border-radius: 50%;
    filter: sepia(0.8) contrast(1.2) brightness(1.1) saturate(0.8);
    transition: filter 0.5s ease;
}

.avatar-vintage:hover {
    filter: sepia(0) contrast(1) brightness(1) saturate(1);
}

/* 8. 故障效果 */
.avatar-glitch {
    border-radius: 50%;
    position: relative;
    animation: glitch-shake 0.3s ease-in-out infinite;
}

.avatar-glitch::before,
.avatar-glitch::after {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    background: inherit;
    border-radius: 50%;
}

.avatar-glitch::before {
    animation: glitch-red 0.3s ease-in-out infinite;
    mix-blend-mode: screen;
    filter: hue-rotate(0deg);
}

.avatar-glitch::after {
    animation: glitch-blue 0.3s ease-in-out infinite;
    mix-blend-mode: screen;
    filter: hue-rotate(180deg);
}

@keyframes glitch-shake {
    0%, 100% { transform: translate(0); }
    10% { transform: translate(-1px, 1px); }
    20% { transform: translate(1px, -1px); }
    30% { transform: translate(-1px, -1px); }
    40% { transform: translate(1px, 1px); }
    50% { transform: translate(-1px, 1px); }
    60% { transform: translate(1px, -1px); }
    70% { transform: translate(-1px, -1px); }
    80% { transform: translate(1px, 1px); }
    90% { transform: translate(-1px, 1px); }
}

@keyframes glitch-red {
    0%, 100% { transform: translate(0); }
    10% { transform: translate(-2px, 0); }
    20% { transform: translate(2px, 0); }
    30% { transform: translate(-2px, 0); }
    40% { transform: translate(2px, 0); }
    50% { transform: translate(-2px, 0); }
    60% { transform: translate(2px, 0); }
    70% { transform: translate(-2px, 0); }
    80% { transform: translate(2px, 0); }
    90% { transform: translate(-2px, 0); }
}

@keyframes glitch-blue {
    0%, 100% { transform: translate(0); }
    10% { transform: translate(2px, 0); }
    20% { transform: translate(-2px, 0); }
    30% { transform: translate(2px, 0); }
    40% { transform: translate(-2px, 0); }
    50% { transform: translate(2px, 0); }
    60% { transform: translate(-2px, 0); }
    70% { transform: translate(2px, 0); }
    80% { transform: translate(-2px, 0); }
    90% { transform: translate(2px, 0); }
}

/* ===== 3D立体类特效 ===== */

/* 1. 立体浮起 */
.avatar-3d-float {
    border-radius: 50%;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.avatar-3d-float:hover {
    transform: translateY(-10px) rotateX(10deg);
    box-shadow: 0 15px 30px rgba(0,0,0,0.4);
}

/* 2. 全息投影 */
.avatar-hologram {
    border-radius: 50%;
    position: relative;
    background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.1) 50%, transparent 70%);
    animation: hologram-shimmer 2s ease-in-out infinite;
    filter: contrast(1.2) brightness(1.1);
}

.avatar-hologram::before {
    content: '';
    position: absolute;
    top: 0; left: 0; right: 0; bottom: 0;
    border-radius: 50%;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(0,255,255,0.2), 
        rgba(255,0,255,0.2), 
        transparent);
    animation: hologram-scan 3s linear infinite;
}

@keyframes hologram-shimmer {
    0%, 100% { opacity: 0.8; }
    50% { opacity: 1; }
}

@keyframes hologram-scan {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* 3. 立体旋转 */
.avatar-3d-rotate {
    border-radius: 50%;
    transform-style: preserve-3d;
    animation: rotate-3d 4s ease-in-out infinite;
}

@keyframes rotate-3d {
    0% { transform: rotateX(0deg) rotateY(0deg); }
    25% { transform: rotateX(90deg) rotateY(0deg); }
    50% { transform: rotateX(90deg) rotateY(90deg); }
    75% { transform: rotateX(0deg) rotateY(90deg); }
    100% { transform: rotateX(0deg) rotateY(0deg); }
}

/* 4. 深度层次 */
.avatar-depth {
    border-radius: 50%;
    position: relative;
}

.avatar-depth::before,
.avatar-depth::after {
    content: '';
    position: absolute;
    top: 5px; left: 5px; right: 5px; bottom: 5px;
    border-radius: 50%;
    background: inherit;
    z-index: -1;
}

.avatar-depth::before {
    transform: translateZ(-10px);
    opacity: 0.7;
}

.avatar-depth::after {
    transform: translateZ(-20px);
    opacity: 0.4;
    top: 10px; left: 10px; right: 10px; bottom: 10px;
}

/* 5. 透视变形 */
.avatar-perspective {
    border-radius: 50%;
    perspective: 1000px;
    animation: perspective-tilt 3s ease-in-out infinite;
}

@keyframes perspective-tilt {
    0%, 100% { transform: perspective(1000px) rotateX(0deg) rotateY(0deg); }
    25% { transform: perspective(1000px) rotateX(15deg) rotateY(15deg); }
    50% { transform: perspective(1000px) rotateX(0deg) rotateY(30deg); }
    75% { transform: perspective(1000px) rotateX(-15deg) rotateY(15deg); }
}
